# 🧮 Calculadora Estratégica de Precios para Dropshipping

Una aplicación web completa para calcular precios de productos de dropshipping con integración a Shopify, soporte para múltiples monedas y persistencia de datos.

## ✨ Características Principales

### 🔢 Calculadora de Precios
- **Cálculo automático** de precios de venta basado en costos y márgenes
- **Punto de equilibrio** - Precio mínimo para cubrir todos los costos
- **Análisis de rentabilidad** con desglose detallado de costos
- **Validación en tiempo real** de todos los campos del formulario
- **Tooltips informativos** para explicar términos técnicos

### 💰 Soporte para Múltiples Monedas
- **8 monedas soportadas**: USD, EUR, COP, MXN, ARS, CLP, PEN, BRL
- **Conversión automática** de símbolos de moneda
- **Persistencia** de la moneda seleccionada

### 📦 Gestión de Inventario
- **Inventario local** con gestión de stock
- **Exportación a CSV** de todos los productos
- **Persistencia automática** en localStorage
- **Edición en línea** de cantidades de stock

### 🛍️ Integración con Shopify
- **Conexión segura** con tu tienda Shopify
- **Importación de productos** existentes desde Shopify
- **Exportación de productos** calculados a Shopify
- **Sincronización de inventario** bidireccional
- **Gestión de variantes** de productos

### 🎨 Interfaz de Usuario
- **Modo oscuro/claro** con persistencia de preferencia
- **Diseño responsive** para móviles y escritorio
- **Animaciones suaves** y transiciones
- **Mensajes de estado** informativos

## 🚀 Instalación y Uso

### Requisitos
- Navegador web moderno (Chrome, Firefox, Safari, Edge)
- Servidor web local (opcional, para desarrollo)

### Instalación Local
1. Clona o descarga este repositorio
2. Abre una terminal en el directorio del proyecto
3. Ejecuta un servidor web local:
   ```bash
   # Con Python 3
   python -m http.server 8000
   
   # Con Node.js (si tienes npx)
   npx serve .
   
   # O simplemente abre index.html en tu navegador
   ```
4. Abre tu navegador en `http://localhost:8000`

## 📋 Guía de Uso

### 1. Calculadora Básica
1. **Selecciona la moneda** de trabajo
2. **Ingresa el nombre** del producto
3. **Especifica los costos**:
   - Costo del proveedor
   - Costo de envío
4. **Configura los porcentajes**:
   - Comisión de pasarela de pago (ej: 4%)
   - Costo de marketing/CAC (ej: 20%)
   - Margen de beneficio deseado (ej: 40%)
5. **Haz clic en "Calcular Precio"**

### 2. Gestión de Inventario
1. Después de calcular un precio, haz clic en **"Añadir al Inventario"**
2. **Edita las cantidades** de stock directamente en la tabla
3. **Elimina productos** usando el botón "Eliminar"
4. **Exporta a CSV** para usar en otras aplicaciones

### 3. Integración con Shopify

#### Configuración Inicial
1. Haz clic en el botón **"🛍️ Shopify"** en los resultados
2. **Obtén tus credenciales** siguiendo las instrucciones en pantalla:
   - Ve a tu panel de Shopify
   - Crea una aplicación privada
   - Copia el Access Token y la URL de tu tienda
3. **Ingresa las credenciales** en los campos correspondientes
4. **Prueba la conexión** antes de guardar

#### Funciones Disponibles
- **📥 Importar Productos**: Trae productos existentes de Shopify
- **📤 Exportar a Shopify**: Envía productos calculados a tu tienda
- **🔄 Sincronizar Inventario**: Actualiza precios y stock en Shopify

## 🔧 Configuración de Shopify

### Paso a Paso para Obtener Credenciales

1. **Accede a tu panel de Shopify**
2. **Ve a Aplicaciones → Desarrollar aplicaciones**
3. **Haz clic en "Crear una aplicación"**
4. **Asigna un nombre** (ej: "Calculadora de Precios")
5. **Configura los permisos** en "Admin API access tokens":
   - `read_products` - Para importar productos
   - `write_products` - Para crear/actualizar productos
   - `read_inventory` - Para leer inventario
   - `write_inventory` - Para actualizar inventario
6. **Instala la aplicación**
7. **Copia el Access Token** generado
8. **Tu URL de tienda** es: `tu-tienda.myshopify.com`

### ⚠️ Seguridad
- **Nunca compartas** tu Access Token públicamente
- **Usa HTTPS** en producción
- **Revoca tokens** que no uses

## 🎯 Casos de Uso

### Para Emprendedores de Dropshipping
- Calcula precios competitivos considerando todos los costos
- Gestiona tu catálogo de productos localmente
- Sincroniza automáticamente con tu tienda Shopify

### Para Agencias de Marketing
- Calcula precios para múltiples clientes
- Exporta reportes en CSV
- Mantén configuraciones separadas por proyecto

### Para Consultores de E-commerce
- Demuestra márgenes de rentabilidad a clientes
- Analiza puntos de equilibrio
- Optimiza estructuras de precios

## 🛠️ Tecnologías Utilizadas

- **HTML5** - Estructura semántica
- **CSS3** - Estilos modernos con variables CSS y Grid/Flexbox
- **JavaScript ES6+** - Lógica de aplicación con clases y async/await
- **LocalStorage** - Persistencia de datos local
- **Shopify Admin API** - Integración con tiendas Shopify
- **Fetch API** - Comunicación con APIs externas

## 📱 Compatibilidad

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Dispositivos móviles (iOS/Android)

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Por favor:

1. Fork el repositorio
2. Crea una rama para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -am 'Agrega nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 🆘 Soporte

Si encuentras algún problema o tienes preguntas:

1. **Revisa la documentación** en este README
2. **Verifica la consola** del navegador para errores
3. **Asegúrate** de que las credenciales de Shopify sean correctas
4. **Prueba** en un navegador diferente

---

**¡Desarrollado con ❤️ para la comunidad de dropshipping!**
