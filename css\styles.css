:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --background-color: #f8f9fa;
    --card-background: #ffffff;
    --text-color: #333;
    --border-color: #dee2e6;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    margin: 0;
    padding: 20px;
    line-height: 1.6;
}

.container {
    max-width: 900px;
    margin: 0 auto;
}

header {
    text-align: center;
    margin-bottom: 40px;
}

header h1 {
    font-weight: 600;
}

.calculator-card, .result-card, .inventory-card {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

h2, h3 {
    margin-top: 0;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.input-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
}

.input-group label {
    font-weight: 400;
    margin-bottom: 5px;
    font-size: 0.9em;
}

.input-group input {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: 'Poppins', sans-serif;
    font-size: 1em;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

button {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s, transform 0.2s;
}

button:hover {
    transform: translateY(-2px);
}

.cta-button {
    background-color: var(--primary-color);
    color: white;
    width: 100%;
    font-size: 1.1em;
    margin-top: 10px;
}

.cta-button:hover {
    background-color: #0056b3;
}

.secondary-button {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.secondary-button:hover {
    background-color: var(--primary-color);
    color: white;
}

/* --- ESTILOS NUEVOS PARA LA SECCIÓN DE ANÁLISIS --- */
.analysis-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.metric-box {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    padding: 20px;
    border-radius: var(--border-radius);
    text-align: center;
}

.metric-box h4 {
    margin: 0 0 10px 0;
    font-weight: 400;
    font-size: 1em;
}

.metric-box .metric-value {
    font-size: 1.8em;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.metric-box small {
    font-size: 0.8em;
    color: var(--secondary-color);
}
/* --- FIN DE ESTILOS NUEVOS --- */

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

thead th {
    font-weight: 600;
}

#result-table tr:last-child {
    font-weight: 600;
    background-color: #f2f9ff;
}

#result-table tr:last-child td:last-child {
    color: var(--success-color);
    font-size: 1.2em;
}

.table-wrapper {
    overflow-x: auto;
}

.delete-btn {
    background-color: var(--danger-color);
    color: white;
    padding: 5px 10px;
    font-size: 0.8em;
}

.delete-btn:hover {
    background-color: #c82333;
}

#inventory-body input[type="number"] {
    width: 60px;
    padding: 5px;
}

.hidden {
    display: none;
}

@media (max-width: 600px) {
    body {
        padding: 10px;
    }
    .calculator-card, .result-card, .inventory-card {
        padding: 20px;
    }
    .analysis-section {
        grid-template-columns: 1fr; /* Apila las métricas en pantallas pequeñas */
    }
}