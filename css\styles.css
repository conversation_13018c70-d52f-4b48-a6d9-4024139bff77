:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --background-color: #f8f9fa;
    --card-background: #ffffff;
    --text-color: #333;
    --border-color: #dee2e6;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Variables para modo oscuro */
[data-theme="dark"] {
    --primary-color: #4dabf7;
    --secondary-color: #adb5bd;
    --background-color: #1a1a1a;
    --card-background: #2d2d2d;
    --text-color: #ffffff;
    --border-color: #404040;
    --success-color: #51cf66;
    --danger-color: #ff6b6b;
    --warning-color: #ffd43b;
    --info-color: #74c0fc;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    margin: 0;
    padding: 20px;
    line-height: 1.6;
    transition: var(--transition);
}

.container {
    max-width: 900px;
    margin: 0 auto;
}

header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

header h1 {
    font-weight: 600;
}

/* Toggle de modo oscuro */
.theme-toggle {
    position: absolute;
    top: 0;
    right: 0;
    background: none;
    border: 2px solid var(--border-color);
    border-radius: 50px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 0.9em;
    color: var(--text-color);
    transition: var(--transition);
}

.theme-toggle:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: none;
}

.info-toggle {
    position: absolute;
    top: 0;
    right: -11rem;
    background: none;
    border: 2px solid var(--info-color);
    border-radius: 50px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 0.9em;
    color: var(--info-color);
    transition: var(--transition);
}

.info-toggle:hover {
    background-color: var(--info-color);
    color: white;
    border-color: var(--info-color);
    transform: none;
}

.calculator-card, .result-card, .inventory-card, .shopify-card {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

h2, h3 {
    margin-top: 0;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.input-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
}

.input-group label {
    font-weight: 400;
    margin-bottom: 5px;
    font-size: 0.9em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-group input,
.input-group select {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: 'Poppins', sans-serif;
    font-size: 1em;
    transition: border-color 0.3s, box-shadow 0.3s;
    background-color: var(--card-background);
    color: var(--text-color);
}

.currency-section {
    background-color: var(--background-color);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.input-group input.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25);
}

.input-group input.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
}

/* Tooltips */
.tooltip {
    position: relative;
    cursor: help;
    font-size: 0.8em;
    color: var(--info-color);
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--text-color);
    color: var(--card-background);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8em;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
    max-width: 200px;
    white-space: normal;
    text-align: center;
    line-height: 1.3;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 115%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--text-color);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Mensajes de error */
.error-message {
    color: var(--danger-color);
    font-size: 0.8em;
    margin-top: 4px;
    display: block;
    min-height: 1.2em;
}

button {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s, transform 0.2s;
}

button:hover {
    transform: translateY(-2px);
}

.cta-button {
    background-color: var(--primary-color);
    color: white;
    width: 100%;
    font-size: 1.1em;
    margin-top: 10px;
}

.cta-button:hover {
    background-color: #0056b3;
}

.secondary-button {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.secondary-button:hover {
    background-color: var(--primary-color);
    color: white;
}

/* --- ESTILOS NUEVOS PARA LA SECCIÓN DE ANÁLISIS --- */
.analysis-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.metric-box {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    padding: 20px;
    border-radius: var(--border-radius);
    text-align: center;
}

.metric-box h4 {
    margin: 0 0 10px 0;
    font-weight: 400;
    font-size: 1em;
}

.metric-box .metric-value {
    font-size: 1.8em;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.metric-box small {
    font-size: 0.8em;
    color: var(--secondary-color);
}
/* --- FIN DE ESTILOS NUEVOS --- */

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

thead th {
    font-weight: 600;
}

#result-table tr:last-child {
    font-weight: 600;
    background-color: #f2f9ff;
}

#result-table tr:last-child td:last-child {
    color: var(--success-color);
    font-size: 1.2em;
}

.table-wrapper {
    overflow-x: auto;
}

.delete-btn {
    background-color: var(--danger-color);
    color: white;
    padding: 5px 10px;
    font-size: 0.8em;
}

.delete-btn:hover {
    background-color: #c82333;
}

#inventory-body input[type="number"] {
    width: 60px;
    padding: 5px;
}

.hidden {
    display: none;
}

/* === ESTILOS PARA SHOPIFY === */
.shopify-card {
    border-left: 4px solid #96bf47; /* Color verde de Shopify */
}

.shopify-config {
    background-color: var(--background-color);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.shopify-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.shopify-actions-section {
    background-color: var(--background-color);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.status-message {
    padding: 12px;
    border-radius: var(--border-radius);
    margin-top: 15px;
    font-weight: 500;
}

.status-message.success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status-message.error {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.status-message.info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border: 1px solid var(--info-color);
}

.progress-section {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    width: 0%;
    transition: width 0.3s ease;
}

.shopify-instructions {
    background-color: var(--background-color);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-top: 20px;
}

.shopify-instructions ol {
    padding-left: 20px;
}

.shopify-instructions li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.shopify-instructions ul {
    margin: 10px 0;
    padding-left: 20px;
}

.shopify-instructions code {
    background-color: var(--card-background);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    border: 1px solid var(--border-color);
}

.warning-box {
    background-color: rgba(255, 193, 7, 0.1);
    border: 1px solid var(--warning-color);
    color: #856404;
    padding: 15px;
    border-radius: var(--border-radius);
    margin-top: 15px;
}

[data-theme="dark"] .warning-box {
    color: var(--warning-color);
}

.result-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* === ESTILOS PARA GUÍA DE PRECIOS === */
.pricing-guide {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
}

.guide-card {
    max-width: 1000px;
    margin: 0 auto;
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.guide-intro {
    font-size: 1.1em;
    color: var(--secondary-color);
    margin-bottom: 30px;
    text-align: center;
}

.guide-sections {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.guide-section {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    background-color: var(--background-color);
}

.guide-section h3 {
    color: var(--primary-color);
    margin-top: 0;
    font-size: 1.3em;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.concept-card {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid var(--primary-color);
}

.concept-card h4 {
    margin-top: 0;
    color: var(--text-color);
    font-size: 1.1em;
}

.concept-card p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.ranges, .tips {
    background-color: var(--background-color);
    padding: 15px;
    border-radius: 6px;
    margin-top: 15px;
}

.ranges ul, .tips ul {
    margin: 10px 0 0 0;
    padding-left: 20px;
}

.ranges li, .tips li {
    margin-bottom: 8px;
}

.range-label {
    font-weight: 600;
    color: var(--primary-color);
}

.range-label.conservative {
    color: var(--success-color);
}

.range-label.moderate {
    color: var(--warning-color);
}

.range-label.aggressive {
    color: var(--danger-color);
}

.recommendation {
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid var(--success-color);
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}

.recommendation p {
    margin: 0;
    color: var(--success-color);
    font-weight: 500;
}

/* Análisis de márgenes de beneficio */
.profit-analysis {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.profit-tier {
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid;
}

.profit-tier.tier-survival,
.tier-survival {
    background-color: rgba(220, 53, 69, 0.1);
    border-left-color: var(--danger-color);
}

.profit-tier.tier-sustainable,
.tier-sustainable {
    background-color: rgba(255, 193, 7, 0.1);
    border-left-color: var(--warning-color);
}

.profit-tier.tier-optimal,
.tier-optimal {
    background-color: rgba(40, 167, 69, 0.1);
    border-left-color: var(--success-color);
}

.profit-tier.tier-premium,
.tier-premium {
    background-color: rgba(123, 97, 255, 0.1);
    border-left-color: var(--primary-color);
}

.profit-tier h5 {
    margin: 0 0 10px 0;
    font-size: 1.1em;
}

.profit-tier p {
    margin: 0 0 10px 0;
    font-weight: 500;
}

.profit-tier ul {
    margin: 0;
    padding-left: 20px;
}

.profit-tier li {
    margin-bottom: 5px;
    font-size: 0.9em;
}

/* Grid de estrategias */
.strategy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.strategy-card {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    transition: var(--transition);
}

.strategy-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.strategy-card h4 {
    margin: 0 0 10px 0;
    color: var(--primary-color);
}

.strategy-card p {
    margin: 0 0 10px 0;
    font-size: 0.9em;
    line-height: 1.5;
}

/* Fórmula y ejemplo */
.equilibrium-formula {
    background-color: var(--background-color);
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
}

.formula {
    background-color: var(--card-background);
    border: 2px solid var(--primary-color);
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    margin-top: 10px;
}

.formula p {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary-color);
}

.equilibrium-example {
    background-color: rgba(23, 162, 184, 0.1);
    border: 1px solid var(--info-color);
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.equilibrium-example ul {
    margin: 10px 0;
    padding-left: 20px;
}

.equilibrium-example li {
    margin-bottom: 5px;
}

.highlight {
    background-color: var(--warning-color);
    color: #000;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 700;
}

.warning {
    color: var(--danger-color);
    font-weight: 600;
    margin-top: 10px;
}

/* Footer de la guía */
.guide-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.close-guide {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    margin-top: 15px;
    transition: var(--transition);
}

.close-guide:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
}

@media (max-width: 600px) {
    body {
        padding: 10px;
    }
    .calculator-card, .result-card, .inventory-card, .shopify-card {
        padding: 20px;
    }
    .analysis-section {
        grid-template-columns: 1fr; /* Apila las métricas en pantallas pequeñas */
    }
    .shopify-actions,
    .action-buttons,
    .result-actions {
        grid-template-columns: 1fr;
    }

    /* Responsive para guía de precios */
    .pricing-guide {
        padding: 10px;
    }

    .guide-card {
        padding: 20px;
    }

    .guide-section {
        padding: 15px;
    }

    .concept-card {
        padding: 15px;
    }

    .strategy-grid {
        grid-template-columns: 1fr;
    }

    .profit-analysis {
        gap: 10px;
    }

    .info-toggle {
        position: static;
        margin-top: 10px;
        width: 100%;
    }
}