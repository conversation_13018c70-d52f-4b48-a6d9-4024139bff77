<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculadora Estratégica de Precios</title>
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>

    <div class="container">
        <header>
            <h1>Calculadora Estratégica de Precios</h1>
            <p>Ingresa los costos y porcentajes para determinar el precio de venta ideal de tu producto.</p>
        </header>

        <main>
            <div class="calculator-card">
                <h2>Parámetros del Producto</h2>
                <form id="cost-form">
                    <div class="input-group">
                        <label for="product-name">Nombre del Producto</label>
                        <input type="text" id="product-name" placeholder="Ej: Kit de Afeitado Sostenible" required>
                    </div>
                    <div class="input-group">
                        <label for="supplier-cost">Costo del Proveedor (USD)</label>
                        <input type="number" id="supplier-cost" placeholder="Ej: 10.50" step="0.01" required>
                    </div>
                     <div class="input-group">
                        <label for="shipping-cost">Costo de Envío (USD)</label>
                        <input type="number" id="shipping-cost" placeholder="Ej: 4.00" step="0.01" required>
                    </div>
                    <div class="input-grid">
                        <div class="input-group">
                            <label for="gateway-fee">Comisión Pasarela (%)</label>
                            <input type="number" id="gateway-fee" value="4" step="0.1" required>
                        </div>
                        <div class="input-group">
                            <label for="marketing-cost">Costo Marketing (CAC) (%)</label>
                            <input type="number" id="marketing-cost" value="20" step="1" required>
                        </div>
                        <div class="input-group">
                            <label for="profit-margin">Margen de Beneficio (%)</label>
                            <input type="number" id="profit-margin" value="40" step="1" required>
                        </div>
                    </div>
                    <button type="submit" class="cta-button">Calcular Precio</button>
                </form>
            </div>

            <div id="result-card" class="result-card hidden">
                <h2>Análisis Estratégico</h2>
                
                <!-- SECCIÓN NUEVA DE ANÁLISIS DE RENTABILIDAD -->
                <div class="analysis-section">
                    <div class="metric-box">
                        <h4>Punto de Equilibrio</h4>
                        <p class="metric-value" id="break-even-price">$0.00</p>
                        <small>Precio mínimo de venta para cubrir todos los costos (margen 0%).</small>
                    </div>
                    <div class="metric-box">
                        <h4>Costos Totales (%)</h4>
                        <p class="metric-value" id="total-costs-percent">0%</p>
                        <small>Porcentaje del precio final destinado a cubrir costos.</small>
                    </div>
                </div>
                <!-- FIN DE LA SECCIÓN NUEVA -->

                <h3>Desglose del Precio de Venta Final</h3>
                <table id="result-table">
                    <!-- Las filas se insertarán aquí con JS -->
                </table>
                <button id="add-to-inventory" class="secondary-button">Añadir al Inventario</button>
            </div>

            <div id="inventory-card" class="inventory-card hidden">
                <h2>Inventario de Productos</h2>
                 <p>Gestiona y exporta la lista de tus productos calculados.</p>
                <div class="table-wrapper">
                    <table>
                        <thead>
                            <tr>
                                <th>Producto</th>
                                <th>Costo Base (USD)</th>
                                <th>Precio Venta (USD)</th>
                                <th>Ganancia Neta (USD)</th>
                                <th>Stock</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody id="inventory-body">
                            <!-- Las filas del inventario se insertarán aquí -->
                        </tbody>
                    </table>
                </div>
                <button id="export-csv" class="secondary-button">Exportar a CSV</button>
            </div>
        </main>
    </div>

    <script src="/js/script.js"></script>
</body>
</html>