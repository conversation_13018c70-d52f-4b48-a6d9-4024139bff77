document.addEventListener('DOMContentLoaded', () => {
    // Selección de Elementos del DOM
    const costForm = document.getElementById('cost-form');
    const resultCard = document.getElementById('result-card');
    const resultTable = document.getElementById('result-table');
    const inventoryCard = document.getElementById('inventory-card');
    const inventoryBody = document.getElementById('inventory-body');
    const addToInventoryBtn = document.getElementById('add-to-inventory');
    const exportCsvBtn = document.getElementById('export-csv');

    // Elementos nuevos para la sección de análisis
    const breakEvenPriceEl = document.getElementById('break-even-price');
    const totalCostsPercentEl = document.getElementById('total-costs-percent');

    let currentCalculation = {};

    // Evento principal: Calcular Precio
    costForm.addEventListener('submit', (e) => {
        e.preventDefault();

        // 1. Obtener y parsear valores del formulario
        const productName = document.getElementById('product-name').value;
        const supplierCost = parseFloat(document.getElementById('supplier-cost').value);
        const shippingCost = parseFloat(document.getElementById('shipping-cost').value);
        const gatewayFeePercent = parseFloat(document.getElementById('gateway-fee').value);
        const marketingCostPercent = parseFloat(document.getElementById('marketing-cost').value);
        const profitMarginPercent = parseFloat(document.getElementById('profit-margin').value);
        
        // 2. Validación
        if (isNaN(supplierCost) || isNaN(shippingCost)) {
            alert('Por favor, ingresa valores numéricos válidos para los costos.');
            return;
        }

        const totalPercentage = gatewayFeePercent + marketingCostPercent + profitMarginPercent;
        if (totalPercentage >= 100) {
            alert('La suma de los porcentajes (comisión, marketing, margen) no puede ser 100% o más.');
            return;
        }

        // 3. Lógica de Cálculo
        const baseCost = supplierCost + shippingCost;
        
        // --- CÁLCULOS NUEVOS ---
        // Cálculo del Punto de Equilibrio (margen de beneficio = 0)
        const breakEvenDivisor = 1 - (gatewayFeePercent / 100) - (marketingCostPercent / 100);
        const breakEvenPrice = baseCost / breakEvenDivisor;
        
        // Cálculo del Precio de Venta Final (con margen de beneficio)
        const finalPriceDivisor = 1 - (gatewayFeePercent / 100) - (marketingCostPercent / 100) - (profitMarginPercent / 100);
        const finalPrice = baseCost / finalPriceDivisor;

        // Cálculo del % total de costos sobre el precio final
        const totalCostsPercent = 100 - profitMarginPercent;
        // --- FIN CÁLCULOS NUEVOS ---

        const gatewayFeeValue = finalPrice * (gatewayFeePercent / 100);
        const marketingCostValue = finalPrice * (marketingCostPercent / 100);
        const netProfit = finalPrice * (profitMarginPercent / 100);
        
        currentCalculation = {
            productName,
            baseCost,
            finalPrice,
            netProfit
        };
        
        // 4. Mostrar resultados
        // Actualizar la nueva sección de análisis de rentabilidad
        breakEvenPriceEl.textContent = `$${breakEvenPrice.toFixed(2)}`;
        totalCostsPercentEl.textContent = `${totalCostsPercent.toFixed(1)}%`;
        
        // Actualizar la tabla de desglose de precio
        renderResultTable(baseCost, gatewayFeeValue, marketingCostValue, netProfit, finalPrice);
        resultCard.classList.remove('hidden');
    });

    // Función para renderizar la tabla de resultados (sin cambios)
    function renderResultTable(base, gateway, marketing, profit, final) {
        resultTable.innerHTML = `
            <tbody>
                <tr><td>Costo Base (Producto + Envío)</td><td>$${base.toFixed(2)}</td></tr>
                <tr><td>Comisión de Pasarela de Pago</td><td>$${gateway.toFixed(2)}</td></tr>
                <tr><td>Costo Adquisición Cliente (Marketing)</td><td>$${marketing.toFixed(2)}</td></tr>
                <tr><td>Ganancia Neta (Utilidad)</td><td>$${profit.toFixed(2)}</td></tr>
                <tr><td><strong>Precio Sugerido al Público</strong></td><td><strong>$${final.toFixed(2)}</strong></td></tr>
            </tbody>
        `;
    }
    
    // El resto del código de JS (añadir a inventario, eliminar, exportar) permanece igual.
    // ... (copia el resto del JS de la versión anterior aquí)
     addToInventoryBtn.addEventListener('click', () => {
        if (!currentCalculation.productName) return;

        inventoryCard.classList.remove('hidden');
        
        const newRow = document.createElement('tr');
        newRow.innerHTML = `
            <td data-label="Producto">${currentCalculation.productName}</td>
            <td data-label="Costo Base (USD)">${currentCalculation.baseCost.toFixed(2)}</td>
            <td data-label="Precio Venta (USD)">${currentCalculation.finalPrice.toFixed(2)}</td>
            <td data-label="Ganancia Neta (USD)">${currentCalculation.netProfit.toFixed(2)}</td>
            <td data-label="Stock"><input type="number" value="1" min="0" class="stock-input"></td>
            <td data-label="Acciones"><button class="delete-btn">Eliminar</button></td>
        `;
        inventoryBody.appendChild(newRow);
        
        costForm.reset();
        resultCard.classList.add('hidden');
        document.getElementById('product-name').focus();
    });

    inventoryBody.addEventListener('click', (e) => {
        if (e.target.classList.contains('delete-btn')) {
            const row = e.target.closest('tr');
            row.remove();
        }
    });

    exportCsvBtn.addEventListener('click', () => {
        const rows = inventoryBody.querySelectorAll('tr');
        if (rows.length === 0) {
            alert('No hay productos en el inventario para exportar.');
            return;
        }

        let csvContent = "data:text/csv;charset=utf-8,";
        csvContent += "Producto,Costo Base (USD),Precio Venta (USD),Ganancia Neta (USD),Stock\n";

        rows.forEach(row => {
            const cols = row.querySelectorAll('td');
            const stockInput = cols[4].querySelector('.stock-input');
            const rowData = [
                `"${cols[0].innerText}"`,
                cols[1].innerText,
                cols[2].innerText,
                cols[3].innerText,
                stockInput.value
            ];
            csvContent += rowData.join(',') + "\n";
        });

        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "inventario_productos.csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
});