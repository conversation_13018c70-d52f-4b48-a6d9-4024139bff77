// ===== CALCULADORA ESTRATÉGICA DE PRECIOS - VERSIÓN MEJORADA =====

/**
 * Clase principal para manejar la calculadora de precios
 */
class PriceCalculator {
    constructor() {
        this.currentCalculation = {};
        this.inventory = this.loadInventory();
        this.currentCurrency = 'USD';
        this.shopifyConfig = { storeUrl: '', accessToken: '' };
        this.currencySymbols = {
            'USD': '$',
            'EUR': '€',
            'COP': '$',
            'MXN': '$',
            'ARS': '$',
            'CLP': '$',
            'PEN': 'S/',
            'BRL': 'R$'
        };
        this.initializeElements();
        this.initializeEventListeners();
        this.initializeTheme();
        this.initializeCurrency();
        this.renderInventory();
    }

    /**
     * Inicializa los elementos del DOM
     */
    initializeElements() {
        // Elementos del formulario
        this.costForm = document.getElementById('cost-form');
        this.productNameInput = document.getElementById('product-name');
        this.supplierCostInput = document.getElementById('supplier-cost');
        this.shippingCostInput = document.getElementById('shipping-cost');
        this.gatewayFeeInput = document.getElementById('gateway-fee');
        this.marketingCostInput = document.getElementById('marketing-cost');
        this.profitMarginInput = document.getElementById('profit-margin');

        // Elementos de resultados
        this.resultCard = document.getElementById('result-card');
        this.resultTable = document.getElementById('result-table');
        this.breakEvenPriceEl = document.getElementById('break-even-price');
        this.totalCostsPercentEl = document.getElementById('total-costs-percent');

        // Elementos de inventario
        this.inventoryCard = document.getElementById('inventory-card');
        this.inventoryBody = document.getElementById('inventory-body');
        this.addToInventoryBtn = document.getElementById('add-to-inventory');
        this.exportCsvBtn = document.getElementById('export-csv');

        // Elementos de tema
        this.themeToggle = document.getElementById('theme-toggle');

        // Elementos de guía de precios
        this.infoToggle = document.getElementById('info-toggle');
        this.pricingGuide = document.getElementById('pricing-guide');
        this.closeGuide = document.getElementById('close-guide');

        // Elementos de Shopify
        this.toggleShopifyBtn = document.getElementById('toggle-shopify');
        this.shopifyCard = document.getElementById('shopify-card');
        this.shopifyStoreUrlInput = document.getElementById('shopify-store-url');
        this.shopifyAccessTokenInput = document.getElementById('shopify-access-token');
        this.testShopifyConnectionBtn = document.getElementById('test-shopify-connection');
        this.saveShopifyConfigBtn = document.getElementById('save-shopify-config');
        this.shopifyStatus = document.getElementById('shopify-status');
        this.shopifyActionsSection = document.getElementById('shopify-actions-section');
        this.importShopifyProductsBtn = document.getElementById('import-shopify-products');
        this.exportToShopifyBtn = document.getElementById('export-to-shopify');
        this.syncInventoryBtn = document.getElementById('sync-inventory');
        this.shopifyProgress = document.getElementById('shopify-progress');
        this.progressFill = document.getElementById('progress-fill');
        this.progressText = document.getElementById('progress-text');

        // Elementos de moneda
        this.currencySelect = document.getElementById('currency-select');
        this.supplierCostLabel = document.getElementById('supplier-cost-label');
        this.shippingCostLabel = document.getElementById('shipping-cost-label');
        this.costHeader = document.getElementById('cost-header');
        this.priceHeader = document.getElementById('price-header');
        this.profitHeader = document.getElementById('profit-header');

        // Elementos de error
        this.errorElements = {
            'supplier-cost': document.getElementById('supplier-cost-error'),
            'shipping-cost': document.getElementById('shipping-cost-error'),
            'gateway-fee': document.getElementById('gateway-fee-error'),
            'marketing-cost': document.getElementById('marketing-cost-error'),
            'profit-margin': document.getElementById('profit-margin-error')
        };
    }

    /**
     * Inicializa los event listeners
     */
    initializeEventListeners() {
        // Evento principal: Calcular Precio
        this.costForm.addEventListener('submit', (e) => this.handleFormSubmit(e));

        // Validación en tiempo real
        this.supplierCostInput.addEventListener('input', () => this.validateInput('supplier-cost'));
        this.shippingCostInput.addEventListener('input', () => this.validateInput('shipping-cost'));
        this.gatewayFeeInput.addEventListener('input', () => this.validateInput('gateway-fee'));
        this.marketingCostInput.addEventListener('input', () => this.validateInput('marketing-cost'));
        this.profitMarginInput.addEventListener('input', () => this.validateInput('profit-margin'));

        // Eventos de inventario
        this.addToInventoryBtn.addEventListener('click', () => this.addToInventory());
        this.exportCsvBtn.addEventListener('click', () => this.exportToCsv());
        this.inventoryBody.addEventListener('click', (e) => this.handleInventoryClick(e));

        // Evento de cambio de tema
        this.themeToggle.addEventListener('click', () => this.toggleTheme());

        // Eventos de guía de precios
        this.infoToggle.addEventListener('click', () => this.showPricingGuide());
        this.closeGuide.addEventListener('click', () => this.hidePricingGuide());

        // Cerrar guía al hacer clic fuera
        this.pricingGuide.addEventListener('click', (e) => {
            if (e.target === this.pricingGuide) {
                this.hidePricingGuide();
            }
        });

        // Evento de cambio de moneda
        this.currencySelect.addEventListener('change', () => this.handleCurrencyChange());

        // Eventos de Shopify
        this.toggleShopifyBtn.addEventListener('click', () => this.toggleShopifySection());
        this.testShopifyConnectionBtn.addEventListener('click', () => this.testShopifyConnection());
        this.saveShopifyConfigBtn.addEventListener('click', () => this.saveShopifyConfiguration());
        this.importShopifyProductsBtn.addEventListener('click', () => this.importShopifyProducts());
        this.exportToShopifyBtn.addEventListener('click', () => this.exportToShopify());
        this.syncInventoryBtn.addEventListener('click', () => this.syncInventory());

        // Auto-guardar en localStorage
        this.costForm.addEventListener('input', () => this.saveFormData());

        // Cargar datos guardados
        this.loadFormData();
        this.loadShopifyConfiguration();
    }

    /**
     * Maneja el envío del formulario
     */
    handleFormSubmit(e) {
        e.preventDefault();

        // Validar todos los campos
        const isValid = this.validateAllInputs();
        if (!isValid) {
            return;
        }

        // Obtener valores del formulario
        const formData = this.getFormData();

        // Realizar cálculos
        const calculation = this.calculatePrice(formData);

        // Guardar cálculo actual
        this.currentCalculation = calculation;

        // Mostrar resultados
        this.displayResults(calculation);
    }

    /**
     * Obtiene los datos del formulario
     */
    getFormData() {
        return {
            productName: this.productNameInput.value.trim(),
            supplierCost: parseFloat(this.supplierCostInput.value) || 0,
            shippingCost: parseFloat(this.shippingCostInput.value) || 0,
            gatewayFeePercent: parseFloat(this.gatewayFeeInput.value) || 0,
            marketingCostPercent: parseFloat(this.marketingCostInput.value) || 0,
            profitMarginPercent: parseFloat(this.profitMarginInput.value) || 0
        };
    }

    /**
     * Valida todos los inputs del formulario
     */
    validateAllInputs() {
        let isValid = true;

        // Validar cada campo
        ['supplier-cost', 'shipping-cost', 'gateway-fee', 'marketing-cost', 'profit-margin'].forEach(fieldId => {
            if (!this.validateInput(fieldId)) {
                isValid = false;
            }
        });

        // Validación adicional: suma de porcentajes
        const formData = this.getFormData();
        const totalPercentage = formData.gatewayFeePercent + formData.marketingCostPercent + formData.profitMarginPercent;

        if (totalPercentage >= 100) {
            this.showError('profit-margin', 'La suma de porcentajes no puede ser 100% o más');
            isValid = false;
        }

        return isValid;
    }

    /**
     * Valida un input específico
     */
    validateInput(fieldId) {
        const input = document.getElementById(fieldId);
        const value = parseFloat(input.value);
        let isValid = true;
        let errorMessage = '';

        // Limpiar clases anteriores
        input.classList.remove('error', 'success');

        // Validaciones específicas por campo
        switch (fieldId) {
            case 'supplier-cost':
            case 'shipping-cost':
                if (isNaN(value) || value < 0) {
                    errorMessage = 'Debe ser un número positivo';
                    isValid = false;
                } else if (value === 0) {
                    errorMessage = 'El costo no puede ser cero';
                    isValid = false;
                }
                break;

            case 'gateway-fee':
            case 'marketing-cost':
            case 'profit-margin':
                if (isNaN(value) || value < 0) {
                    errorMessage = 'Debe ser un porcentaje positivo';
                    isValid = false;
                } else if (value > 100) {
                    errorMessage = 'No puede ser mayor a 100%';
                    isValid = false;
                }
                break;
        }

        // Mostrar resultado de validación
        if (isValid) {
            input.classList.add('success');
            this.clearError(fieldId);
        } else {
            input.classList.add('error');
            this.showError(fieldId, errorMessage);
        }

        return isValid;
    }

    /**
     * Muestra un mensaje de error
     */
    showError(fieldId, message) {
        const errorElement = this.errorElements[fieldId];
        if (errorElement) {
            errorElement.textContent = message;
        }
    }

    /**
     * Limpia el mensaje de error
     */
    clearError(fieldId) {
        const errorElement = this.errorElements[fieldId];
        if (errorElement) {
            errorElement.textContent = '';
        }
    }

    /**
     * Realiza los cálculos de precio
     */
    calculatePrice(formData) {
        const baseCost = formData.supplierCost + formData.shippingCost;

        // Cálculo del Punto de Equilibrio (margen de beneficio = 0)
        const breakEvenDivisor = 1 - (formData.gatewayFeePercent / 100) - (formData.marketingCostPercent / 100);
        const breakEvenPrice = baseCost / breakEvenDivisor;

        // Cálculo del Precio de Venta Final (con margen de beneficio)
        const finalPriceDivisor = 1 - (formData.gatewayFeePercent / 100) - (formData.marketingCostPercent / 100) - (formData.profitMarginPercent / 100);
        const finalPrice = baseCost / finalPriceDivisor;

        // Cálculo del % total de costos sobre el precio final
        const totalCostsPercent = 100 - formData.profitMarginPercent;

        const gatewayFeeValue = finalPrice * (formData.gatewayFeePercent / 100);
        const marketingCostValue = finalPrice * (formData.marketingCostPercent / 100);
        const netProfit = finalPrice * (formData.profitMarginPercent / 100);

        return {
            ...formData,
            baseCost,
            breakEvenPrice,
            finalPrice,
            totalCostsPercent,
            gatewayFeeValue,
            marketingCostValue,
            netProfit
        };
    }

    /**
     * Muestra los resultados del cálculo
     */
    displayResults(calculation) {
        // Actualizar la sección de análisis de rentabilidad
        this.breakEvenPriceEl.textContent = this.formatCurrency(calculation.breakEvenPrice);
        this.totalCostsPercentEl.textContent = `${calculation.totalCostsPercent.toFixed(1)}%`;

        // Actualizar la tabla de desglose de precio
        this.renderResultTable(calculation);
        this.resultCard.classList.remove('hidden');
    }

    /**
     * Renderiza la tabla de resultados
     */
    renderResultTable(calculation) {
        this.resultTable.innerHTML = `
            <tbody>
                <tr><td>Costo Base (Producto + Envío)</td><td>${this.formatCurrency(calculation.baseCost)}</td></tr>
                <tr><td>Comisión de Pasarela de Pago</td><td>${this.formatCurrency(calculation.gatewayFeeValue)}</td></tr>
                <tr><td>Costo Adquisición Cliente (Marketing)</td><td>${this.formatCurrency(calculation.marketingCostValue)}</td></tr>
                <tr><td>Ganancia Neta (Utilidad)</td><td>${this.formatCurrency(calculation.netProfit)}</td></tr>
                <tr><td><strong>Precio Sugerido al Público</strong></td><td><strong>${this.formatCurrency(calculation.finalPrice)}</strong></td></tr>
            </tbody>
        `;
    }

    /**
     * Agrega el producto actual al inventario
     */
    addToInventory() {
        if (!this.currentCalculation.productName) return;

        const product = {
            id: Date.now(),
            name: this.currentCalculation.productName,
            baseCost: this.currentCalculation.baseCost,
            finalPrice: this.currentCalculation.finalPrice,
            netProfit: this.currentCalculation.netProfit,
            stock: 1
        };

        this.inventory.push(product);
        this.saveInventory();
        this.renderInventory();
        this.inventoryCard.classList.remove('hidden');

        // Limpiar formulario
        this.costForm.reset();
        this.resultCard.classList.add('hidden');
        this.productNameInput.focus();

        // Limpiar validaciones
        this.clearAllValidations();
    }

    /**
     * Maneja los clics en el inventario
     */
    handleInventoryClick(e) {
        if (e.target.classList.contains('delete-btn')) {
            const row = e.target.closest('tr');
            const productId = parseInt(row.dataset.productId);
            this.removeFromInventory(productId);
        }
    }

    /**
     * Elimina un producto del inventario
     */
    removeFromInventory(productId) {
        this.inventory = this.inventory.filter(product => product.id !== productId);
        this.saveInventory();
        this.renderInventory();
    }

    /**
     * Renderiza el inventario
     */
    renderInventory() {
        this.inventoryBody.innerHTML = '';

        this.inventory.forEach(product => {
            const row = document.createElement('tr');
            row.dataset.productId = product.id;
            row.innerHTML = `
                <td data-label="Producto">${product.name}</td>
                <td data-label="Costo Base (${this.currentCurrency})">${this.formatCurrency(product.baseCost)}</td>
                <td data-label="Precio Venta (${this.currentCurrency})">${this.formatCurrency(product.finalPrice)}</td>
                <td data-label="Ganancia Neta (${this.currentCurrency})">${this.formatCurrency(product.netProfit)}</td>
                <td data-label="Stock"><input type="number" value="${product.stock}" min="0" class="stock-input" onchange="calculator.updateStock(${product.id}, this.value)"></td>
                <td data-label="Acciones"><button class="delete-btn">Eliminar</button></td>
            `;
            this.inventoryBody.appendChild(row);
        });

        // Mostrar/ocultar inventario
        if (this.inventory.length > 0) {
            this.inventoryCard.classList.remove('hidden');
        }
    }

    /**
     * Actualiza el stock de un producto
     */
    updateStock(productId, newStock) {
        const product = this.inventory.find(p => p.id === productId);
        if (product) {
            product.stock = parseInt(newStock) || 0;
            this.saveInventory();
        }
    }

    /**
     * Exporta el inventario a CSV
     */
    exportToCsv() {
        if (this.inventory.length === 0) {
            alert('No hay productos en el inventario para exportar.');
            return;
        }

        let csvContent = "data:text/csv;charset=utf-8,";
        csvContent += `Producto,Costo Base (${this.currentCurrency}),Precio Venta (${this.currentCurrency}),Ganancia Neta (${this.currentCurrency}),Stock\n`;

        this.inventory.forEach(product => {
            const rowData = [
                `"${product.name}"`,
                product.baseCost.toFixed(2),
                product.finalPrice.toFixed(2),
                product.netProfit.toFixed(2),
                product.stock
            ];
            csvContent += rowData.join(',') + "\n";
        });

        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "inventario_productos.csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * Limpia todas las validaciones
     */
    clearAllValidations() {
        ['supplier-cost', 'shipping-cost', 'gateway-fee', 'marketing-cost', 'profit-margin'].forEach(fieldId => {
            const input = document.getElementById(fieldId);
            input.classList.remove('error', 'success');
            this.clearError(fieldId);
        });
    }

    /**
     * Guarda los datos del formulario en localStorage
     */
    saveFormData() {
        const formData = this.getFormData();
        localStorage.setItem('calculatorFormData', JSON.stringify(formData));
    }

    /**
     * Carga los datos del formulario desde localStorage
     */
    loadFormData() {
        const savedData = localStorage.getItem('calculatorFormData');
        if (savedData) {
            const formData = JSON.parse(savedData);

            if (formData.productName) this.productNameInput.value = formData.productName;
            if (formData.supplierCost) this.supplierCostInput.value = formData.supplierCost;
            if (formData.shippingCost) this.shippingCostInput.value = formData.shippingCost;
            if (formData.gatewayFeePercent) this.gatewayFeeInput.value = formData.gatewayFeePercent;
            if (formData.marketingCostPercent) this.marketingCostInput.value = formData.marketingCostPercent;
            if (formData.profitMarginPercent) this.profitMarginInput.value = formData.profitMarginPercent;
        }
    }

    /**
     * Guarda el inventario en localStorage
     */
    saveInventory() {
        localStorage.setItem('calculatorInventory', JSON.stringify(this.inventory));
    }

    /**
     * Carga el inventario desde localStorage
     */
    loadInventory() {
        const savedInventory = localStorage.getItem('calculatorInventory');
        return savedInventory ? JSON.parse(savedInventory) : [];
    }

    /**
     * Inicializa el tema
     */
    initializeTheme() {
        const savedTheme = localStorage.getItem('calculatorTheme') || 'light';
        this.setTheme(savedTheme);
    }

    /**
     * Cambia entre modo claro y oscuro
     */
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    /**
     * Establece el tema
     */
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('calculatorTheme', theme);

        // Actualizar texto del botón
        if (theme === 'dark') {
            this.themeToggle.innerHTML = '☀️ Modo Claro';
        } else {
            this.themeToggle.innerHTML = '🌙 Modo Oscuro';
        }
    }

    /**
     * Inicializa la configuración de moneda
     */
    initializeCurrency() {
        const savedCurrency = localStorage.getItem('calculatorCurrency') || 'USD';
        this.currencySelect.value = savedCurrency;
        this.handleCurrencyChange();
    }

    /**
     * Maneja el cambio de moneda
     */
    handleCurrencyChange() {
        this.currentCurrency = this.currencySelect.value;
        localStorage.setItem('calculatorCurrency', this.currentCurrency);
        this.updateCurrencyLabels();

        // Si hay un cálculo actual, recalcular con la nueva moneda
        if (this.currentCalculation.productName) {
            this.displayResults(this.currentCalculation);
        }

        // Actualizar inventario
        this.renderInventory();
    }

    /**
     * Actualiza las etiquetas de moneda en la interfaz
     */
    updateCurrencyLabels() {
        const symbol = this.currencySymbols[this.currentCurrency];
        const currency = this.currentCurrency;

        this.supplierCostLabel.textContent = `Costo del Proveedor (${currency})`;
        this.shippingCostLabel.textContent = `Costo de Envío (${currency})`;

        // Actualizar placeholders
        this.supplierCostInput.placeholder = `Ej: 10.50 ${symbol}`;
        this.shippingCostInput.placeholder = `Ej: 4.00 ${symbol}`;

        // Actualizar cabeceras de tabla de inventario
        this.costHeader.textContent = `Costo Base (${currency})`;
        this.priceHeader.textContent = `Precio Venta (${currency})`;
        this.profitHeader.textContent = `Ganancia Neta (${currency})`;
    }

    /**
     * Formatea un número como moneda
     */
    formatCurrency(amount) {
        const symbol = this.currencySymbols[this.currentCurrency];
        return `${symbol}${amount.toFixed(2)}`;
    }

    // ===== MÉTODOS DE SHOPIFY =====

    /**
     * Alterna la visibilidad de la sección de Shopify
     */
    toggleShopifySection() {
        this.shopifyCard.classList.toggle('hidden');
    }

    /**
     * Carga la configuración de Shopify desde localStorage
     */
    loadShopifyConfig() {
        const saved = localStorage.getItem('shopifyConfig');
        return saved ? JSON.parse(saved) : { storeUrl: '', accessToken: '' };
    }

    /**
     * Carga la configuración de Shopify en los inputs
     */
    loadShopifyConfiguration() {
        if (this.shopifyConfig.storeUrl) {
            this.shopifyStoreUrlInput.value = this.shopifyConfig.storeUrl;
        }
        if (this.shopifyConfig.accessToken) {
            this.shopifyAccessTokenInput.value = this.shopifyConfig.accessToken;
        }
    }

    /**
     * Guarda la configuración de Shopify
     */
    saveShopifyConfiguration() {
        const storeUrl = this.shopifyStoreUrlInput.value.trim();
        const accessToken = this.shopifyAccessTokenInput.value.trim();

        if (!storeUrl || !accessToken) {
            this.showShopifyStatus('Por favor, completa todos los campos.', 'error');
            return;
        }

        // Validar formato de URL
        if (!storeUrl.includes('.myshopify.com')) {
            this.showShopifyStatus('La URL debe tener el formato: tu-tienda.myshopify.com', 'error');
            return;
        }

        this.shopifyConfig = { storeUrl, accessToken };
        localStorage.setItem('shopifyConfig', JSON.stringify(this.shopifyConfig));

        this.showShopifyStatus('Configuración guardada correctamente.', 'success');
        this.shopifyActionsSection.classList.remove('hidden');
    }

    /**
     * Muestra un mensaje de estado de Shopify
     */
    showShopifyStatus(message, type = 'info') {
        this.shopifyStatus.textContent = message;
        this.shopifyStatus.className = `status-message ${type}`;
        this.shopifyStatus.classList.remove('hidden');

        // Auto-ocultar después de 5 segundos
        setTimeout(() => {
            this.shopifyStatus.classList.add('hidden');
        }, 5000);
    }

    /**
     * Prueba la conexión con Shopify
     */
    async testShopifyConnection() {
        if (!this.shopifyConfig.storeUrl || !this.shopifyConfig.accessToken) {
            this.showShopifyStatus('Por favor, guarda la configuración primero.', 'error');
            return;
        }

        this.showShopifyStatus('Probando conexión...', 'info');

        try {
            const response = await this.makeShopifyRequest('/admin/api/2023-10/shop.json');

            if (response.shop) {
                this.showShopifyStatus(`✅ Conectado exitosamente a: ${response.shop.name}`, 'success');
                this.shopifyActionsSection.classList.remove('hidden');
            } else {
                throw new Error('Respuesta inválida del servidor');
            }
        } catch (error) {
            this.showShopifyStatus(`❌ Error de conexión: ${error.message}`, 'error');
            this.shopifyActionsSection.classList.add('hidden');
        }
    }

    /**
     * Realiza una petición a la API de Shopify
     */
    async makeShopifyRequest(endpoint, method = 'GET', data = null) {
        const url = `https://${this.shopifyConfig.storeUrl}${endpoint}`;

        const options = {
            method,
            headers: {
                'X-Shopify-Access-Token': this.shopifyConfig.accessToken,
                'Content-Type': 'application/json',
            },
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(url);

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        return await response.json();
    }

    /**
     * Importa productos desde Shopify
     */
    async importShopifyProducts() {
        if (!this.shopifyConfig.storeUrl || !this.shopifyConfig.accessToken) {
            this.showShopifyStatus('Por favor, configura Shopify primero.', 'error');
            return;
        }

        this.showProgress(0, 'Obteniendo productos de Shopify...');

        try {
            const response = await this.makeShopifyRequest('/admin/api/2023-10/products.json?limit=250');
            const products = response.products || [];

            if (products.length === 0) {
                this.showShopifyStatus('No se encontraron productos en Shopify.', 'info');
                this.hideProgress();
                return;
            }

            this.showProgress(50, `Procesando ${products.length} productos...`);

            // Convertir productos de Shopify al formato de la calculadora
            let importedCount = 0;
            for (const product of products) {
                if (product.variants && product.variants.length > 0) {
                    const variant = product.variants[0]; // Usar la primera variante

                    const importedProduct = {
                        id: Date.now() + importedCount,
                        name: product.title,
                        baseCost: parseFloat(variant.price) * 0.6, // Estimar costo base como 60% del precio
                        finalPrice: parseFloat(variant.price),
                        netProfit: parseFloat(variant.price) * 0.4, // Estimar ganancia como 40%
                        stock: variant.inventory_quantity || 0,
                        shopifyId: product.id,
                        shopifyVariantId: variant.id
                    };

                    // Verificar si ya existe
                    const exists = this.inventory.find(p => p.shopifyId === product.id);
                    if (!exists) {
                        this.inventory.push(importedProduct);
                        importedCount++;
                    }
                }
            }

            this.showProgress(100, 'Importación completada');
            this.saveInventory();
            this.renderInventory();

            setTimeout(() => {
                this.hideProgress();
                this.showShopifyStatus(`✅ Se importaron ${importedCount} productos nuevos.`, 'success');
            }, 1000);

        } catch (error) {
            this.hideProgress();
            this.showShopifyStatus(`❌ Error al importar: ${error.message}`, 'error');
        }
    }

    /**
     * Exporta productos del inventario a Shopify
     */
    async exportToShopify() {
        if (!this.shopifyConfig.storeUrl || !this.shopifyConfig.accessToken) {
            this.showShopifyStatus('Por favor, configura Shopify primero.', 'error');
            return;
        }

        const productsToExport = this.inventory.filter(p => !p.shopifyId);

        if (productsToExport.length === 0) {
            this.showShopifyStatus('No hay productos nuevos para exportar.', 'info');
            return;
        }

        this.showProgress(0, `Exportando ${productsToExport.length} productos...`);

        try {
            let exportedCount = 0;

            for (let i = 0; i < productsToExport.length; i++) {
                const product = productsToExport[i];

                const shopifyProduct = {
                    product: {
                        title: product.name,
                        body_html: `Producto calculado con la Calculadora de Precios`,
                        vendor: 'Calculadora de Precios',
                        product_type: 'General',
                        variants: [{
                            price: product.finalPrice.toFixed(2),
                            inventory_quantity: product.stock,
                            inventory_management: 'shopify'
                        }]
                    }
                };

                const response = await this.makeShopifyRequest('/admin/api/2023-10/products.json', 'POST', shopifyProduct);

                if (response.product) {
                    // Actualizar el producto local con los IDs de Shopify
                    product.shopifyId = response.product.id;
                    product.shopifyVariantId = response.product.variants[0].id;
                    exportedCount++;
                }

                // Actualizar progreso
                const progress = ((i + 1) / productsToExport.length) * 100;
                this.showProgress(progress, `Exportando ${i + 1}/${productsToExport.length}...`);

                // Pausa para evitar límites de rate limiting
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            this.saveInventory();
            this.renderInventory();

            setTimeout(() => {
                this.hideProgress();
                this.showShopifyStatus(`✅ Se exportaron ${exportedCount} productos a Shopify.`, 'success');
            }, 1000);

        } catch (error) {
            this.hideProgress();
            this.showShopifyStatus(`❌ Error al exportar: ${error.message}`, 'error');
        }
    }

    /**
     * Sincroniza el inventario con Shopify
     */
    async syncInventory() {
        if (!this.shopifyConfig.storeUrl || !this.shopifyConfig.accessToken) {
            this.showShopifyStatus('Por favor, configura Shopify primero.', 'error');
            return;
        }

        const productsWithShopifyId = this.inventory.filter(p => p.shopifyId && p.shopifyVariantId);

        if (productsWithShopifyId.length === 0) {
            this.showShopifyStatus('No hay productos vinculados con Shopify para sincronizar.', 'info');
            return;
        }

        this.showProgress(0, `Sincronizando ${productsWithShopifyId.length} productos...`);

        try {
            let syncedCount = 0;

            for (let i = 0; i < productsWithShopifyId.length; i++) {
                const product = productsWithShopifyId[i];

                const updateData = {
                    variant: {
                        id: product.shopifyVariantId,
                        price: product.finalPrice.toFixed(2),
                        inventory_quantity: product.stock
                    }
                };

                await this.makeShopifyRequest(
                    `/admin/api/2023-10/variants/${product.shopifyVariantId}.json`,
                    'PUT',
                    updateData
                );

                syncedCount++;

                // Actualizar progreso
                const progress = ((i + 1) / productsWithShopifyId.length) * 100;
                this.showProgress(progress, `Sincronizando ${i + 1}/${productsWithShopifyId.length}...`);

                // Pausa para evitar límites de rate limiting
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            setTimeout(() => {
                this.hideProgress();
                this.showShopifyStatus(`✅ Se sincronizaron ${syncedCount} productos con Shopify.`, 'success');
            }, 1000);

        } catch (error) {
            this.hideProgress();
            this.showShopifyStatus(`❌ Error al sincronizar: ${error.message}`, 'error');
        }
    }

    /**
     * Muestra la barra de progreso
     */
    showProgress(percentage, text) {
        this.shopifyProgress.classList.remove('hidden');
        this.progressFill.style.width = `${percentage}%`;
        this.progressText.textContent = text;
    }

    /**
     * Oculta la barra de progreso
     */
    hideProgress() {
        this.shopifyProgress.classList.add('hidden');
        this.progressFill.style.width = '0%';
    }

    // ===== MÉTODOS DE GUÍA DE PRECIOS =====

    /**
     * Muestra la guía de precios
     */
    showPricingGuide() {
        this.pricingGuide.classList.remove('hidden');
        document.body.style.overflow = 'hidden'; // Prevenir scroll del fondo
    }

    /**
     * Oculta la guía de precios
     */
    hidePricingGuide() {
        this.pricingGuide.classList.add('hidden');
        document.body.style.overflow = 'auto'; // Restaurar scroll
    }
}

// Inicializar la aplicación cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    // Crear instancia global para acceso desde HTML (onchange en inputs de stock)
    window.calculator = new PriceCalculator();
});